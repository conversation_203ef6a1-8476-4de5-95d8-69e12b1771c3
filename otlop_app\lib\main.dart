import 'package:flutter/material.dart';
import 'package:otlop_app/HomePage.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:otlop_app/ld_themes.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/providers/location_provider.dart';
import 'package:otlop_app/screens/auth/phone_auth_screen.dart';
import 'package:otlop_app/services/api_service.dart';
import 'package:otlop_app/services/auth_service.dart';
import 'package:otlop_app/services/order_service.dart';
import 'package:otlop_app/services/restaurant_service.dart';
import 'package:otlop_app/services/location_service.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Check if user is logged in
  final prefs = await SharedPreferences.getInstance();
  final isLoggedIn = prefs.containsKey('user_id');

  runApp(MyApp(isLoggedIn: isLoggedIn));
}

class MyApp extends StatefulWidget {
  final bool isLoggedIn;

  const MyApp({super.key, required this.isLoggedIn});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override


  Widget build(BuildContext context) {
    // Create service instances
    final apiService = ApiService();
    final authService = AuthService(apiService: apiService);
    final orderService = OrderService(apiService: apiService);
    final restaurantService = RestaurantService(apiService: apiService);
    final locationService = LocationService(apiService: apiService);

    return MultiProvider(
    providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider(authService: authService)),
        ChangeNotifierProvider(create: (context) => RestaurantProvider(restaurantService: restaurantService)),
        ChangeNotifierProvider(create: (context) => OrderProvider(orderService: orderService)),
        ChangeNotifierProvider(create: (context) => LocationProvider(locationService: locationService)),
      ],
      child: MaterialApp(
        title: 'أطلب',
        debugShowCheckedModeBanner: false,
        theme: lightTheme,
        darkTheme: darkTheme,
        themeMode: ThemeMode.light,
        localizationsDelegates: const [
          AppLocalizations.delegate, // Add this line
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: AppLocalizations.supportedLocales,
        home: widget.isLoggedIn ? const HomePage() : const PhoneAuthScreen(),
        routes: {
          '/home': (context) => const HomePage(),
          '/login': (context) => const PhoneAuthScreen(),
        },
      ),
    );
  }
}
