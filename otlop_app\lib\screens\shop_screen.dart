import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/customWidgets.dart';
import 'package:otlop_app/ld_themes.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/providers/location_provider.dart';
import 'package:otlop_app/screens/restaurant_detail_screen.dart';
import 'package:otlop_app/models/location_model.dart';

class ShopScreen extends StatefulWidget {
  const ShopScreen({super.key});

  @override
  State<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends State<ShopScreen> with WidgetsBindingObserver {
  bool _isLoadingLocation = false;
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();
  OverlayEntry? _overlayEntry;
  final FocusNode _searchFocusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    // Register as an observer to detect app lifecycle changes
    WidgetsBinding.instance.addObserver(this);

    // Add listener to search focus node
    _searchFocusNode.addListener(() {
      if (_searchFocusNode.hasFocus) {
        _showSearchOverlay();
      } else {
        _removeSearchOverlay();
      }
    });

    // Initialize data loading
    _initializeData();
  }

  Future<void> _initializeData() async {
    if (_hasInitialized) return;

    // Load user locations first
    await _loadUserLocations();

    // Then load restaurants
    await _loadRestaurants();

    _hasInitialized = true;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed && _hasInitialized) {
      // Refresh data when app resumes
      _loadRestaurants();
    }
  }

  @override
  void dispose() {
    // Unregister the observer
    WidgetsBinding.instance.removeObserver(this);

    _searchController.dispose();
    _searchFocusNode.dispose();
    _removeSearchOverlay();
    super.dispose();
  }

  Future<void> _loadRestaurants() async {
    if (!mounted) return;

    try {
      final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);

      // Clear any existing error before fetching
      restaurantProvider.clearError();

      // Fetch restaurants with proper error handling
      await restaurantProvider.fetchRestaurants();

      debugPrint('Restaurants loaded successfully: ${restaurantProvider.restaurants.length}');
    } catch (e) {
      debugPrint('Error in _loadRestaurants: $e');
      // Error is handled in the provider, just log here
    }
  }

  // Load user saved locations
  Future<void> _loadUserLocations() async {
    if (!mounted) return;

    try {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      await locationProvider.loadLocations();
    } catch (e) {
      // Error handling is done in the provider
    }
  }

  // Build the app bar with search and location dropdown
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      title: Row(
        children: [
          // Location dropdown
          Consumer<LocationProvider>(
            builder: (context, locationProvider, child) {
              final selectedLocation = locationProvider.selectedLocation;

              return PopupMenuButton<UserLocation>(
                offset: const Offset(0, 40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: primaryPurple.withAlpha(20),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: primaryPurple,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        selectedLocation?.name ?? 'اختر موقعك',
                        style: const TextStyle(
                          color: primaryPurple,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const Icon(
                        Icons.arrow_drop_down,
                        color: primaryPurple,
                        size: 16,
                      ),
                    ],
                  ),
                ),
                onSelected: (UserLocation location) {
                  locationProvider.selectLocation(location);
                },
                itemBuilder: (BuildContext context) {
                  final locations = locationProvider.locations;

                  if (locations.isEmpty) {
                    return [
                      PopupMenuItem<UserLocation>(
                        enabled: false,
                        child: Text(
                          'لا توجد مواقع محفوظة',
                          style: TextStyle(
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      PopupMenuItem<UserLocation>(
                        child: const Row(
                          children: [
                            Icon(
                              Icons.add_location,
                              color: primaryPurple,
                            ),
                            SizedBox(width: 8),
                            Text('إضافة موقع جديد'),
                          ],
                        ),
                        onTap: () {
                          // Add a small delay to allow the popup to close
                          Future.delayed(const Duration(milliseconds: 100), () {
                            _getCurrentLocation();
                          });
                        },
                      ),
                    ];
                  }

                  final items = locations.map((location) {
                    return PopupMenuItem<UserLocation>(
                      value: location,
                      child: Row(
                        children: [
                          Icon(
                            location.isDefault ? Icons.home : Icons.location_on,
                            color: location.id == selectedLocation?.id
                                ? primaryPurple
                                : Colors.grey,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              location.name,
                              style: TextStyle(
                                fontWeight: location.id == selectedLocation?.id
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                                color: location.id == selectedLocation?.id
                                    ? primaryPurple
                                    : Colors.black,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList();

                  // Add option to add new location
                  items.add(
                    PopupMenuItem<UserLocation>(
                      child: const Row(
                        children: [
                          Icon(
                            Icons.add_location,
                            color: primaryPurple,
                          ),
                          SizedBox(width: 8),
                          Text('إضافة موقع جديد'),
                        ],
                      ),
                      onTap: () {
                        // Add a small delay to allow the popup to close
                        Future.delayed(const Duration(milliseconds: 100), () {
                          _getCurrentLocation();
                        });
                      },
                    ),
                  );

                  return items;
                },
              );
            },
          ),

          const SizedBox(width: 8),

          // Search field
          Expanded(
            child: CompositedTransformTarget(
              link: _layerLink,
              child: Container(
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  controller: _searchController,
                  focusNode: _searchFocusNode,
                  textAlignVertical: TextAlignVertical.center,
                  textInputAction: TextInputAction.search,
                  onSubmitted: _performSearch,
                  decoration: InputDecoration(
                    hintText: 'ابحث عن مطعم...',
                    hintStyle: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                    prefixIcon: const Icon(
                      Icons.search,
                      color: primaryPurple,
                      size: 20,
                    ),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, size: 16),
                            onPressed: () {
                              _searchController.clear();
                              _performSearch('');
                            },
                          )
                        : null,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      actions: [
        // Refresh button
        IconButton(
          icon: const Icon(Icons.refresh),
          color: primaryPurple,
          onPressed: () {
            _loadRestaurants();
          },
          tooltip: 'Refresh restaurants',
        ),
      ],
    );
  }

  // Show search overlay
  void _showSearchOverlay() {
    final overlay = Overlay.of(context);
    final renderBox = context.findRenderObject() as RenderBox;
    final size = renderBox.size;

    _removeSearchOverlay();

    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width - 100, // Adjust width as needed
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: const Offset(0, 40), // Adjust offset as needed
          child: Material(
            elevation: 4,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8),
              constraints: BoxConstraints(
                maxHeight: 200,
                minWidth: size.width - 100,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    child: Text(
                      'اضغط Enter للبحث',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                  ),
                  const Divider(height: 1),
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Icon(
                          Icons.history,
                          color: Colors.grey[600],
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'بحث سابق',
                          style: TextStyle(
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  // Remove search overlay
  void _removeSearchOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  // Perform search
  void _performSearch(String query) {
    _removeSearchOverlay();

    if (query.isEmpty) {
      // If query is empty, reload all restaurants
      _loadRestaurants();
      return;
    }

    setState(() {
      _isSearching = true;
    });

    // Get restaurant provider
    final restaurantProvider = Provider.of<RestaurantProvider>(context, listen: false);

    // Execute search
    restaurantProvider.searchRestaurants(query).then((_) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });
      }
    }).catchError((error) {
      if (mounted) {
        setState(() {
          _isSearching = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching restaurants: $error')),
        );
      }
    });
  }

  // Get current location
  Future<void> _getCurrentLocation() async {
    if (_isLoadingLocation) return;

    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      final position = await locationProvider.getCurrentPosition();

      if (position != null && mounted) {
        // Show dialog to save this location
        await showDialog<void>(
          context: context,
          builder: (context) => AddLocationDialog(
            latitude: position.latitude,
            longitude: position.longitude,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error getting location: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Don't automatically load restaurants on build
    // Only load when user explicitly clicks retry

    return Scaffold(
      appBar: _buildAppBar(context),
      body: Column(children: [

        // Main Content
        Expanded(
          child: Consumer<RestaurantProvider>(
            builder: (context, restaurantProvider, child) {
              // Handle loading state
              if (restaurantProvider.isLoading && restaurantProvider.restaurants.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('جاري تحميل المطاعم...'),
                    ],
                  ),
                );
              }
              // Handle error state
              if (restaurantProvider.error != null) {
                // Check if it's an unauthorized error
                final errorMessage = restaurantProvider.error!;
                if (errorMessage.contains('Unauthorized')) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 60,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Session Expired',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Your session has expired. Please log in again.',
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton.icon(
                          onPressed: () async {
                            // Get the auth provider
                            final authProvider = Provider.of<AuthProvider>(context, listen: false);
                            // Store navigator before async operation
                            final navigator = Navigator.of(context);
                            // Logout
                            await authProvider.logout();
                            // Check if still mounted before navigating
                            if (mounted) {
                              navigator.pushReplacementNamed('/login');
                            }
                          },
                          icon: const Icon(Icons.login),
                          label: const Text('Log In'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                          ),
                        ),
                      ],
                    ),
                  );
                }
                // General error

                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'خطأ في تحميل المطاعم',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Text(
                          errorMessage,
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: () {
                          _loadRestaurants();
                        },
                        icon: const Icon(Icons.refresh),
                        label: const Text('Retry'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }

              final restaurants = restaurantProvider.restaurants;

              // Handle empty state - show load restaurants button if no data has been loaded yet
              if (restaurants.isEmpty) {
                // Check if this is the initial state (no error and no loading)
                final isInitialState = !restaurantProvider.isLoading && restaurantProvider.error == null;

                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        isInitialState ? Icons.restaurant_menu : Icons.restaurant,
                        color: Colors.grey,
                        size: 60,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        isInitialState ? 'مرحباً بك في طلباتي' : 'لا توجد مطاعم',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        isInitialState
                          ? 'اضغط على الزر أدناه لتحميل المطاعم المتاحة'
                          : 'جرب البحث عن مطعم مختلف أو تحقق مرة أخرى لاحقاً',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _loadRestaurants,
                        icon: Icon(isInitialState ? Icons.restaurant : Icons.refresh),
                        label: Text(isInitialState ? 'تحميل المطاعم' : 'إعادة المحاولة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }

              // Show restaurant list with pull-to-refresh
              return RefreshIndicator(
                onRefresh: _loadRestaurants,
                color: Theme.of(context).colorScheme.primary,
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  children: [
                    // Restaurant count
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Text(
                          '${restaurants.length} مطعم',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),

                    // Restaurant List
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: restaurants.length,
                      itemBuilder: (context, index) {
                        final restaurant = restaurants[index];
                        return GestureDetector(
                            onTap: () async {
                              // Select the restaurant and navigate to its page
                              restaurantProvider.selectRestaurant(restaurant);
                              restaurantProvider.fetchRestaurantMenu(restaurant.id.toString());

                              await Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => RestaurantDetailScreen(
                                    restaurantId: restaurant.id.toString(),
                                  ),
                                ),
                              );

                              // Refresh restaurants when returning from detail screen
                              if (mounted) {
                                _loadRestaurants();
                              }
                            },
                            child: ShopCard(
                              shopName: restaurant.name,
                              openedStatus: restaurant.isOpen,
                              distance: restaurant.distance ?? 0.0,
                              imageUrl: restaurant.image != null && restaurant.image!.isNotEmpty
                                  ? restaurant.image!
                                  : 'assets/images/rest.jpg',
                              cuisineType: restaurant.restaurantTypeName ?? 'Restaurant',
                              deliveryFee: restaurant.deliveryFee ?? 2.0,
                            ),
                        );
                      },
                    ),

                    // Loading indicator at the bottom when refreshing
                    if (restaurantProvider.isLoading && restaurants.isNotEmpty)
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ]),
    );
  }


}



// Add Location Dialog
class AddLocationDialog extends StatefulWidget {
  final double latitude;
  final double longitude;

  const AddLocationDialog({
    super.key,
    required this.latitude,
    required this.longitude,
  });

  @override
  AddLocationDialogState createState() => AddLocationDialogState();
}

class AddLocationDialogState extends State<AddLocationDialog> {
  final TextEditingController _nameController = TextEditingController();
  String? _address;
  bool _isDefault = false;
  bool _isLoading = true;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _getAddressFromCoordinates();
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _getAddressFromCoordinates() async {
    try {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      final address = await locationProvider.getAddressFromCoordinates(
        widget.latitude,
        widget.longitude,
      );

      if (mounted) {
        setState(() {
          _address = address;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _address = 'Could not determine address';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveLocation() async {
    if (_nameController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a name for this location')),
      );
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final locationProvider = Provider.of<LocationProvider>(context, listen: false);
      final newLocation = UserLocation(
        name: _nameController.text.trim(),
        latitude: widget.latitude,
        longitude: widget.longitude,
        address: _address,
        isDefault: _isDefault,
      );

      final success = await locationProvider.addLocation(newLocation);

      if (success && mounted) {
        Navigator.pop(context); // Close add dialog
        Navigator.pop(context); // Close location management dialog
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving location: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Add New Location',
        style: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: _isLoading
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: CircularProgressIndicator(),
              ),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Location Name',
                    hintText: 'e.g. Home, Work, School',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  autofocus: true,
                ),
                const SizedBox(height: 16),
                Text(
                  'Address:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700],
                  ),
                ),
                Text(
                  _address ?? 'Unknown address',
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Checkbox(
                      value: _isDefault,
                      onChanged: (value) {
                        setState(() {
                          _isDefault = value ?? false;
                        });
                      },
                    ),
                    const Text('Set as default location'),
                  ],
                ),
              ],
            ),
      actions: [
        TextButton(
          onPressed: _isSaving ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: Colors.grey[700],
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isSaving ? null : _saveLocation,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: _isSaving
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white,
                  ),
                )
              : const Text('Save'),
        ),
      ],
    );
  }
}
