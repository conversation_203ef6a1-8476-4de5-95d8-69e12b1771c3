const sequelize = require("../config/database");
const { Op } = require('sequelize');
const { Sequelize } = require('sequelize');
const { Customer, CustomerPlaces, Order } = require('../models/init-models')(sequelize);

exports.getAllCustomers = async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const search = req.query.search || '';
        const offset = (page - 1) * limit;

        const whereClause = {
            Status: { [Op.ne]: 2 }
        };

        if (search) {
            whereClause[Op.or] = [
                { PhoneNum: { [Op.like]: `%${search}%` } }
            ];
        }

        const { count, rows: customers } = await Customer.findAndCountAll({
            where: whereClause,
            attributes: ['CustomerID', 'PhoneNum', 'JoinDate', 'IsBanned', 'Status'],
            limit,
            offset,
            order: [['CustomerID', 'DESC']]
        });

        res.status(200).json({
            success: true,
            data: customers,
            pagination: {
                currentPage: page,
                totalPages: Math.ceil(count / limit),
                totalItems: count,
                itemsPerPage: limit,
                hasNext: page < Math.ceil(count / limit),
                hasPrevious: page > 1
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

exports.getCustomerDetails = async (req, res) => {
    try {
        const { id } = req.params;

        const [customer, places, orderCount, lastOrder] = await Promise.all([
            Customer.findOne({
                where: { CustomerID: id, Status: { [Op.ne]: 2 } },
                attributes: ['CustomerID', 'PhoneNum', 'JoinDate', 'IsBanned']
            }),
            CustomerPlaces.findAll({
                where: { CustomerID: id },
                attributes: ['PlaceID', 'PlaceName', 'Latitude', 'Longitude']
            }),
            Order.count({
                where: { CustomerID: id }
            }),
            Order.findOne({
                where: { CustomerID: id },
                order: [['OrderDate', 'DESC']],
                attributes: ['OrderDate']
            })
        ]);

        if (!customer) {
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على العميل'
            });
        }

        res.status(200).json({
            success: true,
            data: {
                ...customer.toJSON(),
                CustomerPlaces: places,
                OrderCount: orderCount,
                LastOrderDate: lastOrder?.OrderDate
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

exports.createCustomer = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { phoneNum, username, locationLatitude, locationLongitude } = req.body;

        if (!phoneNum) {
            await transaction.rollback();
            return res.status(400).json({
                success: false,
                message: 'رقم الهاتف مطلوب'
            });
        }

        const existingCustomer = await Customer.findOne({
            where: { PhoneNum: phoneNum },
            transaction
        });

        if (existingCustomer) {
            await transaction.rollback();
            return res.status(400).json({
                success: false,
                message: 'رقم الهاتف موجود مسبقاً'
            });
        }

        const customer = await Customer.create({
            PhoneNum: phoneNum,
            Username: username,
            LocationLatitude: locationLatitude,
            LocationLongitude: locationLongitude,
            JoinDate: Sequelize.fn('GETDATE'),
            Status: 1,
            IsBanned: false
        }, { transaction });

        await transaction.commit();
        res.status(201).json({
            success: true,
            data: customer
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};

exports.updateCustomerStatus = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { customerId } = req.params;
        const { isBanned } = req.body;

        const customer = await Customer.findOne({
            where: {
                CustomerID: customerId,
                Status: { [Op.ne]: 2 }
            },
            transaction
        });

        if (!customer) {
            await transaction.rollback();
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على بيانات العميل'
            });
        }

        await customer.update({
            IsBanned: isBanned,
            UpdateDate: Sequelize.fn('GETDATE')
        }, { transaction });

        await transaction.commit();
        res.status(200).json({
            success: true,
            message: 'تم تحديث حالة العميل بنجاح'
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};

exports.addCustomerPlace = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { customerId } = req.params;
        const { placeName, longitude, latitude, address, isDefault } = req.body;

        if (!placeName || !longitude || !latitude) {
            await transaction.rollback();
            return res.status(400).json({
                success: false,
                message: 'اسم المكان والموقع مطلوبين'
            });
        }

        const customer = await Customer.findOne({
            where: {
                CustomerID: customerId,
                Status: { [Op.ne]: 2 }
            },
            transaction
        });

        if (!customer) {
            await transaction.rollback();
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على بيانات العميل'
            });
        }

        // If this is set as default, unset any existing default locations
        if (isDefault) {
            await CustomerPlaces.update(
                { IsDefault: 0 },
                {
                    where: { CustomerID: customerId, IsDefault: 1 },
                    transaction
                }
            );
        }

        const place = await CustomerPlaces.create({
            CustomerID: customerId,
            PlaceName: placeName,
            Longitude: longitude,
            Latitude: latitude,
            Address: address,
            IsDefault: isDefault ? 1 : 0,
            InsertDate: Sequelize.fn('GETDATE')
        }, { transaction });

        await transaction.commit();
        res.status(201).json({
            success: true,
            data: place
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};

exports.deleteCustomerPlace = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { placeId } = req.params;

        const place = await CustomerPlaces.findByPk(placeId, { transaction });
        if (!place) {
            await transaction.rollback();
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على المكان'
            });
        }

        // Check if this is a default location
        const isDefault = place.IsDefault === 1;

        await place.destroy({ transaction });

        // If this was a default location, set another location as default if available
        if (isDefault) {
            const anotherPlace = await CustomerPlaces.findOne({
                where: { CustomerID: place.CustomerID },
                transaction
            });

            if (anotherPlace) {
                await anotherPlace.update({ IsDefault: 1 }, { transaction });
            }
        }

        await transaction.commit();
        res.status(200).json({
            success: true,
            message: 'تم حذف المكان بنجاح'
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};

// Get all places for a customer
exports.getCustomerPlaces = async (req, res) => {
    try {
        console.log("Attempting to fetch customer places for customerId:", req.params.customerId);
        const { customerId } = req.params;

        const places = await CustomerPlaces.findAll({
            where: { CustomerID: customerId },
            attributes: ['PlaceID', 'PlaceName', 'Latitude', 'Longitude','InsertDate'],
            order: [['InsertDate', 'DESC']]
        });

        console.log("Fetched places:", places);
        res.status(200).json({
            success: true,
            data: places
        });
    } catch (error) {
        console.error("Error fetching customer places:", error);
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
};

// Update a customer place
exports.updateCustomerPlace = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { placeId } = req.params;
        const { placeName, longitude, latitude} = req.body;

        const place = await CustomerPlaces.findByPk(placeId, { transaction });
        if (!place) {
            await transaction.rollback();
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على المكان'
            });
        }

        // If this is set as default, unset any existing default locations
        if (isDefault) {
            await CustomerPlaces.update(
                { IsDefault: 0 },
                {
                    where: {
                        CustomerID: place.CustomerID,
                        IsDefault: 1,
                        PlaceID: { [Op.ne]: placeId }
                    },
                    transaction
                }
            );
        }

        // Update the place
        await place.update({
            PlaceName: placeName || place.PlaceName,
            Longitude: longitude || place.Longitude,
            Latitude: latitude || place.Latitude,
            UpdateDate: Sequelize.fn('GETDATE')
        }, { transaction });

        await transaction.commit();
        res.status(200).json({
            success: true,
            data: place
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};

// Set a place as default
exports.setDefaultPlace = async (req, res) => {
    const transaction = await sequelize.transaction();
    try {
        const { placeId } = req.params;

        const place = await CustomerPlaces.findByPk(placeId, { transaction });
        if (!place) {
            await transaction.rollback();
            return res.status(404).json({
                success: false,
                message: 'لم يتم العثور على المكان'
            });
        }

        // Unset any existing default locations for this customer
        await CustomerPlaces.update(
            { IsDefault: 0 },
            {
                where: {
                    CustomerID: place.CustomerID,
                    IsDefault: 1
                },
                transaction
            }
        );

        // Set this place as default
        await place.update({
            IsDefault: 1,
            UpdateDate: Sequelize.fn('GETDATE')
        }, { transaction });

        await transaction.commit();
        res.status(200).json({
            success: true,
            message: 'تم تعيين المكان كافتراضي بنجاح'
        });
    } catch (error) {
        await transaction.rollback();
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
};
