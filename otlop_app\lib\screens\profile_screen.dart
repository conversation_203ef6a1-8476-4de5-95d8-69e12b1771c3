import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/providers/restaurant_provider.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // No need to check authentication here, we'll do it in the build method
  }

  // Handle logout process
  Future<void> _handleLogout(
    AuthProvider authProvider,
    NavigatorState navigator,
    ScaffoldMessengerState scaffoldMessenger
  ) async {
    try {
      await authProvider.logout();

      if (mounted) {
        // Navigate to login screen
        navigator.pushReplacementNamed('/login');
      }
    } catch (e) {
      if (mounted) {
        // Show error message
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text('Error logging out: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (_isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        if (!authProvider.isAuthenticated) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  'Please log in to view your profile',
                  style: TextStyle(fontSize: 18),
                ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pushNamed(context, '/login');
                  },
                  child: const Text('Log In'),
                ),
              ],
            ),
          );
        }

        final user = authProvider.user;

        return Center(
          child: Padding(
            padding: const EdgeInsets.all(30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Profile Image
                ClipRRect(
                  borderRadius: const BorderRadius.all(Radius.circular(100)),
                  child: Image.asset(
                    'assets/images/rest3.jpg', // Default profile image
                    height: 200,
                    width: 200,
                    fit: BoxFit.cover,
                  ),
                ),

                // User Name
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      user?.name ?? 'User',
                      style: const TextStyle(
                        fontFamily: 'Alx',
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    const Icon(
                      Icons.verified_user_outlined,
                      color: Colors.amber,
                      size: 20,
                    ),
                  ]
                ),

                const Divider(),

                // Location (placeholder)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: () {},
                      child: const Row(
                        children: [
                          Icon(Icons.location_on_outlined),
                          Text(
                            'Not Available',
                            style: TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Text(
                      ': الموقع',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),

                // Phone Number
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ElevatedButton(
                      onPressed: () {},
                      child: Row(
                        children: [
                          const Icon(Icons.phone_android_outlined),
                          Text(
                            user?.phone ?? 'No Phone',
                            style: const TextStyle(
                              fontFamily: 'Cairo',
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Text(
                      ': الهاتف',
                      style: TextStyle(
                        fontFamily: 'Cairo',
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                  ],
                ),

                // Stats Container
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(
                      color: Colors.grey.withAlpha(102), // 0.4 * 255 = 102
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(10),
                    child: IntrinsicHeight(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          // Orders Count
                          Column(
                            children: [
                              const Text(
                                'عدد الطلبات',
                                style: TextStyle(fontFamily: 'Alx', fontSize: 20),
                              ),
                              Consumer<OrderProvider>(
                                builder: (context, orderProvider, child) {
                                  final orderCount = orderProvider.orders.length;
                                  return Text(
                                    orderCount > 0 ? orderCount.toString() : '0',
                                    style: TextStyle(
                                      fontFamily: 'Alx',
                                      color: Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 20,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),

                          const VerticalDivider(),

                          // Favorite Restaurants Count
                          Column(
                            children: [
                              const Text(
                                'المطاعم المفضلة',
                                style: TextStyle(fontFamily: 'Alx', fontSize: 20),
                              ),
                              Consumer<RestaurantProvider>(
                                builder: (context, restaurantProvider, child) {
                                  // Count favorite restaurants (placeholder)
                                  return Text(
                                    '0',
                                    style: TextStyle(
                                      fontFamily: 'Alx',
                                      color: Theme.of(context).colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 20,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // Logout Button
                ElevatedButton.icon(
                  onPressed: () {
                    // Store context references before async operations
                    final navigator = Navigator.of(context);
                    final scaffoldMessenger = ScaffoldMessenger.of(context);

                    // Handle logout
                    _handleLogout(authProvider, navigator, scaffoldMessenger);
                  },
                  icon: const Icon(Icons.logout),
                  label: const Text('Logout'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
