const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Customer', {
    CustomerID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    PhoneNum: {
      type: DataTypes.STRING(15),
      allowNull: false,
      unique: "UQ__Customer__DF8F1A029B9F1DB1"
    },
    LocationLongitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    LocationLatitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    JoinDate: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    Status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    IsBanned: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false
    },
    IsVerified: {
      type: DataTypes.BOOLEAN,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'Customer',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Customer__A4AE64B8C35C0EF6",
        unique: true,
        fields: [
          { name: "CustomerID" },
        ]
      },
      {
        name: "UQ__Customer__DF8F1A029B9F1DB1",
        unique: true,
        fields: [
          { name: "PhoneNum" },
        ]
      },
    ]
  });
};
